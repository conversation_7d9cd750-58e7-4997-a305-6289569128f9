# VitePress Documentation Template

A clean and customizable VitePress documentation template with a well-organized structure and modern design.

## Deploy
Deploy with EdgeOne Pages.

[![EdgeOne Pages deploy](https://cdnstatic.tencentcs.com/edgeone/pages/deploy.svg)](https://edgeone.ai/pages/new?template=vitepress-template)

## Features

- 📚 Well-organized documentation structure
- 🎨 Customized theme and styling
- 📱 Responsive design
- 🔍 Full-text search
- 📦 Easy to deploy
- 🚀 Fast and lightweight

## Directory Structure

```
.
├── .vitepress/          # VitePress configuration
│   ├── config.mts       # Site configuration
│   └── theme/           # Custom theme files
│       └── style.css    # Custom styles
├── pages/              # Documentation pages
│   ├── index.md        # Home page
│   ├── quick-start/    # Quick start guide
│   ├── advanced/       # Advanced topics
│   ├── deployment/     # Deployment guides
│   └── examples/       # Examples
├── dist/               # Build output directory
├── package.json        # Project dependencies
├── edgeone.json        # Project deployment parameters
└── .gitignore         # Git ignore rules
```

## Getting Started

1. **Installation**

```bash
# Clone the repository
git clone [your-repo-url]

# Install dependencies
npm install
```

2. **Development**

```bash
# Start local development server
npm run dev
```

3. **Build**

```bash
# Build for production
npm run build
```

4. **Preview**

```bash
# Preview production build
npm run preview
```

## Documentation Structure

- **Quick Start**: Basic setup and configuration guide
- **Advanced**: In-depth topics and customization
- **Examples**: Markdown and API usage examples
- **Deployment**: Deployment guides for various platforms

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.