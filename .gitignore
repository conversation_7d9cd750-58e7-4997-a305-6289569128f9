# Dependencies
node_modules
.pnp
.pnp.js

# Production
dist
cache

# VitePress
.vitepress/cache
.vitepress/dist

# Local env files
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# System Files
.DS_Store
Thumbs.db

# Lock files
# Uncomment if you want to ignore package-lock.json
# package-lock.json
# yarn.lock
# pnpm-lock.yaml 